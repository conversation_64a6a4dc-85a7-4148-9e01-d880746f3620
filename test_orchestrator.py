"""
Test script for Report Manager Orchestrator

This script tests the basic functionality of the orchestrator system
without requiring an actual OpenAI API key.
"""

import asyncio
import sys
import os
from unittest.mock import Mock, AsyncMock

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from orchestrator import ReportManagerOrchestrator, UserContext, TaskType, ContextResult
from flow import FlowController
from agents.router_agent import RouterAgent
from agents.query_agent import QueryAgent
from utils.io import IOHandler


class MockLLM:
    """Mock LLM for testing without API key"""
    
    async def ainvoke(self, messages):
        """Mock LLM response"""
        mock_response = Mock()
        mock_response.content = '''
        {
            "intent": "retrieve sales reports",
            "task_type": "query",
            "entities": ["sales", "reports"],
            "parameters": {"type": "sales"},
            "confidence": 0.9,
            "reasoning": "User wants to see sales reports",
            "suggested_agents": ["query_agent"]
        }
        '''
        return mock_response


async def test_orchestrator():
    """Test the orchestrator system"""
    print("🚀 Testing Report Manager Orchestrator...")
    
    try:
        # Create mock orchestrator
        orchestrator = ReportManagerOrchestrator(
            openai_api_key="test_key",
            model_name="gpt-4",
            temperature=0.1
        )
        
        # Replace LLM with mock
        orchestrator.llm = MockLLM()
        
        print("✅ Orchestrator initialized")
        
        # Test context creation
        print("\n📝 Testing context creation...")
        context_result = await orchestrator.create_context("Show me all sales reports")
        
        print(f"   Intent: {context_result.context.intent}")
        print(f"   Task Type: {context_result.context.task_type}")
        print(f"   Entities: {context_result.context.entities}")
        print(f"   Confidence: {context_result.context.confidence}")
        
        # Test router agent
        print("\n🔀 Testing router agent...")
        router = RouterAgent()
        agents = await router.route(context_result.context, context_result.suggested_agents)
        print(f"   Selected agents: {agents}")
        
        # Test query agent
        print("\n🔍 Testing query agent...")
        query_agent = QueryAgent()
        query_result = await query_agent.process(context_result.context)
        
        print(f"   Query successful: {query_result.success}")
        print(f"   Query type: {query_result.query_type}")
        print(f"   Data keys: {list(query_result.data.keys()) if isinstance(query_result.data, dict) else 'N/A'}")
        
        # Test flow controller
        print("\n🌊 Testing flow controller...")
        flow_controller = FlowController(orchestrator)
        
        # Test simple flow
        result = await flow_controller.execute_simple_flow("Show me all reports")
        
        print(f"   Flow successful: {result.success}")
        print(f"   Agents used: {result.agents_used}")
        print(f"   Execution time: {result.execution_time:.3f}s")
        
        # Test IO handler
        print("\n📄 Testing IO handler...")
        io_handler = IOHandler()
        
        # Test formatting
        test_data = {"reports": [{"id": 1, "title": "Test Report"}]}
        formatted_json = io_handler.format_output(test_data, "json")
        formatted_text = io_handler.format_output(test_data, "text")
        
        print(f"   JSON format length: {len(formatted_json)} chars")
        print(f"   Text format length: {len(formatted_text)} chars")
        
        print("\n🎉 All tests passed! The orchestrator system is working correctly.")
        
        # Show example usage
        print("\n📋 Example Usage:")
        print("   python main.py query 'Show me all completed reports'")
        print("   python main.py interactive")
        print("   python main.py status")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_agent_capabilities():
    """Test individual agent capabilities"""
    print("\n🔧 Testing individual agent capabilities...")
    
    # Test router agent capabilities
    router = RouterAgent()
    available_agents = router.list_available_agents()
    print(f"   Available agents: {available_agents}")
    
    for agent_name in available_agents:
        info = router.get_agent_info(agent_name)
        print(f"   {agent_name}: {info.get('capabilities', [])}")
    
    # Test query agent with different query types
    query_agent = QueryAgent()
    
    test_contexts = [
        UserContext(
            original_text="Show me all reports",
            intent="retrieve all reports",
            task_type=TaskType.QUERY,
            entities=["reports"],
            parameters={},
            confidence=0.9,
            metadata={}
        ),
        UserContext(
            original_text="Find sales reports",
            intent="search for sales reports",
            task_type=TaskType.QUERY,
            entities=["sales", "reports"],
            parameters={"type": "sales"},
            confidence=0.8,
            metadata={}
        ),
        UserContext(
            original_text="Count reports by status",
            intent="aggregate reports by status",
            task_type=TaskType.DATA_ANALYSIS,
            entities=["reports", "status"],
            parameters={},
            confidence=0.85,
            metadata={}
        )
    ]
    
    for i, context in enumerate(test_contexts, 1):
        print(f"\n   Test {i}: {context.original_text}")
        result = await query_agent.process(context)
        print(f"      Query type: {result.query_type}")
        print(f"      Success: {result.success}")
        if result.data and isinstance(result.data, dict):
            print(f"      Data keys: {list(result.data.keys())}")


if __name__ == "__main__":
    print("Report Manager Orchestrator Test Suite")
    print("=" * 50)
    
    async def run_all_tests():
        # Run main orchestrator test
        main_test_passed = await test_orchestrator()
        
        # Run agent capability tests
        await test_agent_capabilities()
        
        print("\n" + "=" * 50)
        if main_test_passed:
            print("🎯 Test suite completed successfully!")
            print("\nNext steps:")
            print("1. Set up your OpenAI API key in .env file")
            print("2. Install dependencies: pip install -r requirements.txt")
            print("3. Run: python main.py interactive")
        else:
            print("❌ Some tests failed. Please check the errors above.")
    
    # Run the tests
    asyncio.run(run_all_tests())
