# Report Manager Orchestrator - API Guide

This guide explains how to run and use the Report Manager Orchestrator as a REST API service.

## 🚀 Quick Start

### 1. Setup Environment

```bash
# Copy environment template
cp .env.example .env

# Edit .env and add your OpenAI API key
OPENAI_API_KEY=your_actual_api_key_here
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Start the API Server

```bash
# Using the CLI command (recommended)
python main.py serve

# Or directly with uvicorn
python api_server.py

# Custom host and port
python main.py serve --host 0.0.0.0 --port 8080
```

### 4. Access the API

- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **Alternative Docs**: http://localhost:8000/redoc

## 📡 API Endpoints

### Core Endpoints

#### `POST /query`
Process a user query using the orchestrator.

**Request Body:**
```json
{
  "text": "Show me all completed sales reports",
  "complex_flow": false,
  "output_format": "json",
  "save_to_file": false,
  "filename": null
}
```

**Response:**
```json
{
  "success": true,
  "result": {
    "reports": [
      {"id": 1, "title": "Q1 Sales Report", "status": "completed"}
    ]
  },
  "context": {
    "intent": "retrieve completed sales reports",
    "task_type": "query",
    "entities": ["sales", "reports", "completed"],
    "confidence": 0.9
  },
  "agents_used": ["query_agent"],
  "execution_time": 1.234,
  "flow_id": null,
  "error": null
}
```

#### `GET /status`
Get system status and available agents.

**Response:**
```json
{
  "system": "Report Manager Orchestrator",
  "status": "running",
  "available_agents": ["query_agent", "router_agent", "report_agent"],
  "agent_capabilities": {
    "query_agent": {
      "task_types": ["query", "data_analysis"],
      "capabilities": ["data_retrieval", "filtering", "search"]
    }
  },
  "active_flows": 0
}
```

### Agent Management

#### `GET /agents`
List all available agents and their capabilities.

#### `GET /agents/{agent_name}`
Get detailed information about a specific agent.

### Flow Management

#### `GET /flows`
List all active flows.

#### `GET /flows/{flow_id}`
Get status of a specific flow.

### Utility Endpoints

#### `POST /test-connection`
Test connection to LLM service.

#### `GET /health`
Health check endpoint.

## 🐍 Python Client Usage

### Using the Provided Client

```python
from api_client_example import OrchestratorAPIClient

# Initialize client
client = OrchestratorAPIClient("http://localhost:8000")

# Simple query
result = client.query("Show me all reports")
print(f"Success: {result['success']}")
print(f"Result: {result['result']}")

# Complex flow
complex_result = client.query(
    "Generate comprehensive sales analysis", 
    complex_flow=True
)
print(f"Flow ID: {complex_result['flow_id']}")

# Check system status
status = client.get_status()
print(f"Available agents: {status['available_agents']}")
```

### Using Requests Directly

```python
import requests

# Simple query
response = requests.post("http://localhost:8000/query", json={
    "text": "Find all marketing reports",
    "complex_flow": False
})

result = response.json()
print(result)

# Get system status
status_response = requests.get("http://localhost:8000/status")
status = status_response.json()
print(status)
```

## 🌐 JavaScript/Frontend Usage

### Using Fetch API

```javascript
// Simple query
async function queryOrchestrator(text) {
    const response = await fetch('http://localhost:8000/query', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            text: text,
            complex_flow: false
        })
    });
    
    const result = await response.json();
    return result;
}

// Usage
queryOrchestrator("Show me all reports")
    .then(result => {
        console.log('Success:', result.success);
        console.log('Result:', result.result);
        console.log('Agents used:', result.agents_used);
    })
    .catch(error => console.error('Error:', error));

// Get system status
async function getStatus() {
    const response = await fetch('http://localhost:8000/status');
    const status = await response.json();
    return status;
}
```

### Using Axios

```javascript
import axios from 'axios';

const api = axios.create({
    baseURL: 'http://localhost:8000'
});

// Query
const queryResult = await api.post('/query', {
    text: 'Analyze customer trends',
    complex_flow: true
});

console.log(queryResult.data);

// Status
const statusResult = await api.get('/status');
console.log(statusResult.data);
```

## 🔧 Configuration

### Environment Variables

```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4
OPENAI_TEMPERATURE=0.1

# API Server Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_RELOAD=true

# Logging
LOG_LEVEL=INFO
```

### CORS Configuration

For production, update the CORS settings in `api_server.py`:

```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://yourdomain.com"],  # Specific domains
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)
```

## 🧪 Testing

### Test the API Server

```bash
# Run comprehensive API tests
python main.py api-test

# Or run the client example directly
python api_client_example.py
```

### Example Test Queries

```bash
curl -X POST "http://localhost:8000/query" \
     -H "Content-Type: application/json" \
     -d '{"text": "Show me all reports", "complex_flow": false}'

curl -X GET "http://localhost:8000/status"

curl -X GET "http://localhost:8000/health"
```

## 🚀 Production Deployment

### Using Docker (Recommended)

Create a `Dockerfile`:

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "api_server.py"]
```

Build and run:

```bash
docker build -t report-orchestrator .
docker run -p 8000:8000 -e OPENAI_API_KEY=your_key report-orchestrator
```

### Using Gunicorn

```bash
pip install gunicorn

gunicorn api_server:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8000
```

### Environment Setup for Production

```bash
# Set production environment variables
export OPENAI_API_KEY=your_production_key
export API_HOST=0.0.0.0
export API_PORT=8000
export API_RELOAD=false
export LOG_LEVEL=WARNING
```

## 📊 Monitoring and Logging

### Logs

- API logs: `api_server.log`
- Orchestrator logs: `orchestrator.log`

### Health Monitoring

```bash
# Check if API is healthy
curl http://localhost:8000/health

# Monitor system status
curl http://localhost:8000/status
```

## 🔍 Troubleshooting

### Common Issues

1. **API Key Not Set**
   ```
   Error: OPENAI_API_KEY environment variable not set
   ```
   Solution: Set your OpenAI API key in `.env` file

2. **Port Already in Use**
   ```
   Error: [Errno 48] Address already in use
   ```
   Solution: Use a different port or kill the existing process

3. **Import Errors**
   ```
   ModuleNotFoundError: No module named 'fastapi'
   ```
   Solution: Install dependencies with `pip install -r requirements.txt`

### Debug Mode

Run with debug logging:

```bash
LOG_LEVEL=DEBUG python api_server.py
```

## 📚 API Documentation

Once the server is running, visit:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

These provide interactive API documentation where you can test endpoints directly.
