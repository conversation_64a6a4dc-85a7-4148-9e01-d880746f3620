# Development Guide

This guide covers development setup, architecture, and contribution guidelines for the Report Manager Orchestrator.

## 🏗️ Project Structure

```
report-manager-orchestrator/
├── src/report_manager/          # Main package
│   ├── core/                    # Core orchestration logic
│   │   ├── orchestrator.py      # Main orchestrator class
│   │   ├── models.py            # Data models and types
│   │   └── flow.py              # Flow control and execution
│   ├── agents/                  # Agent implementations
│   │   ├── base.py              # Base agent classes
│   │   ├── router_agent.py      # Agent routing logic
│   │   ├── query_agent.py       # Query processing agent
│   │   └── connectors.py        # Data connectors
│   ├── api/                     # REST API implementation
│   │   ├── server.py            # FastAPI server
│   │   └── routes/              # API route modules
│   ├── utils/                   # Utility modules
│   │   ├── io.py                # Input/output handling
│   │   ├── config.py            # Configuration management
│   │   └── logging.py           # Logging setup
│   └── schemas/                 # Data schemas and metadata
├── tests/                       # Test suite
│   ├── unit/                    # Unit tests
│   ├── integration/             # Integration tests
│   └── conftest.py              # Pytest configuration
├── scripts/                     # Utility scripts
├── docs/                        # Documentation
├── examples/                    # Usage examples
├── config/                      # Configuration files
├── main.py                      # Main entry point
├── setup.py                     # Package setup
├── pyproject.toml              # Modern Python packaging
└── requirements.txt            # Dependencies
```

## 🚀 Development Setup

### 1. Clone and Setup

```bash
git clone <repository-url>
cd report-manager-orchestrator

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Install in development mode
pip install -e .
```

### 2. Environment Configuration

```bash
# Copy environment template
cp config/.env.example .env

# Edit .env and add your API keys
OPENAI_API_KEY=your_openai_api_key_here
```

### 3. Run Tests

```bash
# Run all tests
python main.py test

# Run specific test categories
pytest tests/unit/ -v
pytest tests/integration/ -v

# Run with coverage
pytest --cov=src/report_manager tests/
```

## 🏛️ Architecture Overview

### Core Components

1. **Orchestrator** (`core/orchestrator.py`)
   - Main coordination logic
   - LLM integration for context creation
   - Agent workflow execution

2. **Flow Controller** (`core/flow.py`)
   - Manages execution flows
   - Handles simple and complex workflows
   - Tracks flow status and progress

3. **Agent System** (`agents/`)
   - Base agent framework
   - Specialized agent implementations
   - Agent registry and routing

4. **API Layer** (`api/`)
   - REST API endpoints
   - Request/response handling
   - Authentication and validation

### Data Flow

```
User Input → Orchestrator → LLM Context Creation → Agent Routing → Agent Execution → Result Formatting → Response
```

## 🔧 Adding New Features

### Adding a New Agent

1. **Create Agent Class**

```python
# src/report_manager/agents/my_agent.py
from .base import BaseAgent
from ..core.models import UserContext

class MyAgent(BaseAgent):
    def __init__(self):
        super().__init__("my_agent")
        self.capabilities = ["my_capability"]
        self.priority = 2

    async def process(self, context: UserContext):
        # Implementation here
        return {"result": "processed"}
```

2. **Register Agent**

```python
# Update src/report_manager/agents/__init__.py
from .my_agent import MyAgent

# Update router_agent.py to include new agent
```

3. **Add Tests**

```python
# tests/unit/test_my_agent.py
import pytest
from report_manager.agents.my_agent import MyAgent

@pytest.mark.asyncio
async def test_my_agent_process():
    agent = MyAgent()
    # Test implementation
```

### Adding API Endpoints

1. **Create Route Module**

```python
# src/report_manager/api/routes/my_routes.py
from fastapi import APIRouter

router = APIRouter(prefix="/my-feature", tags=["my-feature"])

@router.get("/")
async def my_endpoint():
    return {"message": "Hello from my feature"}
```

2. **Register Routes**

```python
# Update src/report_manager/api/server.py
from .routes.my_routes import router as my_router
app.include_router(my_router)
```

## 🧪 Testing Guidelines

### Test Structure

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test component interactions
- **API Tests**: Test REST API endpoints

### Writing Tests

```python
import pytest
from unittest.mock import Mock, AsyncMock

@pytest.mark.asyncio
async def test_my_function():
    # Arrange
    mock_dependency = Mock()

    # Act
    result = await my_function(mock_dependency)

    # Assert
    assert result is not None
    mock_dependency.method.assert_called_once()
```

### Test Fixtures

Use fixtures in `conftest.py` for common test data:

```python
@pytest.fixture
def sample_context():
    return UserContext(...)
```

## 📝 Code Style

### Python Style Guide

- Follow PEP 8
- Use type hints
- Write docstrings for all public functions
- Maximum line length: 88 characters

### Code Formatting

```bash
# Format code
black src/ tests/

# Sort imports
isort src/ tests/

# Type checking
mypy src/
```

### Documentation

- Use Google-style docstrings
- Include type hints
- Provide usage examples

```python
def my_function(param: str) -> Dict[str, Any]:
    """
    Brief description of the function.

    Args:
        param: Description of parameter

    Returns:
        Description of return value

    Raises:
        ValueError: When param is invalid

    Example:
        >>> result = my_function("test")
        >>> print(result)
        {'status': 'success'}
    """
```

## 🔍 Debugging

### Logging

```python
from report_manager.utils.logging import get_logger

logger = get_logger(__name__)
logger.info("Debug message")
logger.error("Error message")
```

### Development Server

```bash
# Run with debug mode
python main.py serve --reload

# Check logs
tail -f orchestrator.log
```

## 🚢 Deployment

### Production Setup

1. **Environment Variables**

```bash
export OPENAI_API_KEY=your_production_key
export API_HOST=0.0.0.0
export API_PORT=8000
export LOG_LEVEL=WARNING
```

2. **Production Server**

```bash
# Using Gunicorn
gunicorn src.report_manager.api.server:app -w 4 -k uvicorn.workers.UvicornWorker

# Using Docker
docker build -t report-orchestrator .
docker run -p 8000:8000 report-orchestrator
```

## 🤝 Contributing

### Pull Request Process

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Update documentation
6. Submit pull request

### Commit Messages

Use conventional commit format:

```
feat: add new agent for data analysis
fix: resolve circular import issue
docs: update API documentation
test: add unit tests for orchestrator
```

## 📚 Resources

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [LangChain Documentation](https://python.langchain.com/)
- [OpenAI API Reference](https://platform.openai.com/docs/)
- [Pytest Documentation](https://docs.pytest.org/)