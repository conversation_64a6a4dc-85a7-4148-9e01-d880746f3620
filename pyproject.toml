[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "report-manager-orchestrator"
version = "1.0.0"
description = "AI-powered orchestrator for report management"
readme = "docs/README.md"
requires-python = ">=3.9"
license = {text = "MIT"}
authors = [
    {name = "Report Manager Team", email = "<EMAIL>"},
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
]

[project.scripts]
report-manager = "report_manager.cli:main"
report-manager-server = "report_manager.api.server:main"

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"