"""
FastAPI Server for Report Manager Orchestrator

This module provides a REST API interface for the orchestrator system.
"""

import asyncio
import os
import uuid
from typing import Optional, Dict, Any, List
from datetime import datetime

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from dotenv import load_dotenv
from loguru import logger

from ..core.orchestrator import ReportManagerOrchestrator
from ..core.flow import FlowController
from ..utils.io import IOHandler
from ..core.models import OrchestrationResult

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="Report Manager Orchestrator API",
    description="AI-powered orchestrator that processes user text, creates context with LLM, and coordinates agent calls",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global instances
orchestrator: Optional[ReportManagerOrchestrator] = None
flow_controller: Optional[FlowController] = None
io_handler = IOHandler()
active_flows: Dict[str, Dict[str, Any]] = {}


# Pydantic models for API
class QueryRequest(BaseModel):
    text: str
    complex_flow: bool = False
    output_format: str = "json"
    save_to_file: bool = False
    filename: Optional[str] = None


class QueryResponse(BaseModel):
    success: bool
    result: Any
    context: Dict[str, Any]
    agents_used: List[str]
    execution_time: float
    flow_id: Optional[str] = None
    error: Optional[str] = None


class FlowStatusResponse(BaseModel):
    flow_id: str
    total_steps: int
    completed_steps: int
    current_step: str
    steps: List[Dict[str, Any]]


class SystemStatusResponse(BaseModel):
    system: str
    status: str
    available_agents: List[str]
    agent_capabilities: Dict[str, Any]
    active_flows: int


def initialize_system():
    """Initialize the orchestrator system"""
    global orchestrator, flow_controller

    # Check for Azure OpenAI configuration
    azure_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
    azure_deployment = os.getenv("AZURE_OPENAI_DEPLOYMENT")
    azure_api_key = os.getenv("AZURE_OPENAI_API_KEY")

    # Get OpenAI API key (fallback)
    openai_api_key = os.getenv("OPENAI_API_KEY")

    # Determine which service to use
    if azure_endpoint and azure_deployment and azure_api_key:
        # Use Azure OpenAI
        logger.info("Using Azure OpenAI")
        orchestrator = ReportManagerOrchestrator(
            openai_api_key=azure_api_key,
            use_azure=True,
            azure_endpoint=f"https://{azure_endpoint}.openai.azure.com/",
            azure_deployment=azure_deployment,
            azure_api_version=os.getenv("AZURE_OPENAI_API_VERSION", "2024-11-01-preview"),
            temperature=float(os.getenv("OPENAI_TEMPERATURE", "0.1"))
        )
    elif openai_api_key:
        # Use regular OpenAI
        logger.info("Using OpenAI")
        orchestrator = ReportManagerOrchestrator(
            openai_api_key=openai_api_key,
            model_name=os.getenv("OPENAI_MODEL", "gpt-4"),
            temperature=float(os.getenv("OPENAI_TEMPERATURE", "0.1"))
        )
    else:
        raise ValueError("No API key found. Set either OPENAI_API_KEY or AZURE_OPENAI_API_KEY")
    
    # Initialize flow controller
    flow_controller = FlowController(orchestrator)
    
    logger.info("API system initialized successfully")


@app.on_event("startup")
async def startup_event():
    """Initialize system on startup"""
    try:
        initialize_system()
        logger.info("API server started successfully")
    except Exception as e:
        logger.error(f"Failed to initialize system: {e}")
        raise


@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Report Manager Orchestrator API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }


@app.post("/query", response_model=QueryResponse)
async def process_query(request: QueryRequest, background_tasks: BackgroundTasks):
    """
    Process a user query using the orchestrator
    """
    try:
        logger.info(f"Processing query: {request.text[:100]}...")
        
        # Process the query
        if request.complex_flow:
            flow_id = str(uuid.uuid4())
            result = await flow_controller.execute_complex_flow(request.text, flow_id)
            
            # Store flow info for status tracking
            active_flows[flow_id] = {
                "created_at": datetime.now().isoformat(),
                "query": request.text,
                "status": "completed" if result.success else "failed"
            }
        else:
            result = await flow_controller.execute_simple_flow(request.text)
            flow_id = None
        
        # Prepare response
        response = QueryResponse(
            success=result.success,
            result=result.result,
            context={
                "intent": result.context.intent,
                "task_type": result.context.task_type.value,
                "entities": result.context.entities,
                "confidence": result.context.confidence,
                "parameters": result.context.parameters
            },
            agents_used=result.agents_used,
            execution_time=result.execution_time,
            flow_id=flow_id,
            error=result.error
        )
        
        # Save to file if requested
        if request.save_to_file and request.filename:
            background_tasks.add_task(
                save_result_to_file,
                response.dict(),
                request.filename,
                request.output_format
            )
        
        return response
        
    except Exception as e:
        logger.error(f"Error processing query: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/status", response_model=SystemStatusResponse)
async def get_system_status():
    """
    Get system status and available agents
    """
    try:
        if orchestrator is None:
            raise HTTPException(status_code=503, detail="System not initialized")
        
        return SystemStatusResponse(
            system="Report Manager Orchestrator",
            status="running",
            available_agents=orchestrator.router_agent.list_available_agents(),
            agent_capabilities={
                agent: orchestrator.router_agent.get_agent_info(agent)
                for agent in orchestrator.router_agent.list_available_agents()
            },
            active_flows=len(active_flows)
        )
        
    except Exception as e:
        logger.error(f"Error getting status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/flows/{flow_id}", response_model=FlowStatusResponse)
async def get_flow_status(flow_id: str):
    """
    Get the status of a specific flow
    """
    try:
        if flow_controller is None:
            raise HTTPException(status_code=503, detail="System not initialized")
        
        flow_status = flow_controller.get_flow_status(flow_id)
        
        if flow_status is None:
            raise HTTPException(status_code=404, detail="Flow not found")
        
        return FlowStatusResponse(**flow_status)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting flow status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/flows")
async def list_active_flows():
    """
    List all active flows
    """
    return {
        "active_flows": active_flows,
        "count": len(active_flows)
    }


@app.post("/test-connection")
async def test_llm_connection():
    """
    Test connection to LLM service
    """
    try:
        if orchestrator is None:
            raise HTTPException(status_code=503, detail="System not initialized")
        
        # Test with a simple query
        test_result = await orchestrator.create_context("Hello, test connection")
        
        return {
            "success": True,
            "test_query": "Hello, test connection",
            "response_received": True,
            "confidence": test_result.context.confidence,
            "intent": test_result.context.intent
        }
        
    except Exception as e:
        logger.error(f"Connection test failed: {e}")
        return {
            "success": False,
            "error": str(e)
        }


@app.get("/agents")
async def list_agents():
    """
    List all available agents and their capabilities
    """
    try:
        if orchestrator is None:
            raise HTTPException(status_code=503, detail="System not initialized")
        
        agents = orchestrator.router_agent.list_available_agents()
        
        return {
            "agents": [
                {
                    "name": agent,
                    "info": orchestrator.router_agent.get_agent_info(agent)
                }
                for agent in agents
            ],
            "count": len(agents)
        }
        
    except Exception as e:
        logger.error(f"Error listing agents: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/agents/{agent_name}")
async def get_agent_info(agent_name: str):
    """
    Get detailed information about a specific agent
    """
    try:
        if orchestrator is None:
            raise HTTPException(status_code=503, detail="System not initialized")
        
        agent_info = orchestrator.router_agent.get_agent_info(agent_name)
        
        if not agent_info:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        return {
            "name": agent_name,
            "info": agent_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting agent info: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def save_result_to_file(result_data: Dict[str, Any], filename: str, format_type: str):
    """Background task to save result to file"""
    try:
        await io_handler.save_to_file(result_data, filename, format_type)
        logger.info(f"Result saved to {filename}")
    except Exception as e:
        logger.error(f"Failed to save result to {filename}: {e}")


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "system_initialized": orchestrator is not None
    }


if __name__ == "__main__":
    import uvicorn
    
    # Configure logging
    logger.add("api_server.log", rotation="1 day", retention="7 days")
    
    # Run the server
    uvicorn.run(
        "api_server:app",
        host=os.getenv("API_HOST", "0.0.0.0"),
        port=int(os.getenv("API_PORT", "8000")),
        reload=os.getenv("API_RELOAD", "true").lower() == "true",
        log_level=os.getenv("LOG_LEVEL", "info").lower()
    )
