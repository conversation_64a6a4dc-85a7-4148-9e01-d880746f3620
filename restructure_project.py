#!/usr/bin/env python3
"""
Project Restructuring Script

This script reorganizes the project into a proper Python package structure.
"""

import os
import shutil
from pathlib import Path

def create_directory_structure():
    """Create the new directory structure"""
    
    # Define the new structure
    structure = {
        "src": {
            "report_manager": {
                "__init__.py": "",
                "core": {
                    "__init__.py": "",
                    "orchestrator.py": "orchestrator.py",
                    "models.py": "models.py",
                    "flow.py": "flow.py"
                },
                "agents": {
                    "__init__.py": "",
                    "base.py": "",
                    "router_agent.py": "agents/router_agent.py",
                    "query_agent.py": "agents/query_agent.py",
                    "connectors.py": "agents/connectors.py"
                },
                "api": {
                    "__init__.py": "",
                    "server.py": "api_server.py",
                    "routes": {
                        "__init__.py": "",
                        "query.py": "",
                        "status.py": "",
                        "agents.py": ""
                    }
                },
                "utils": {
                    "__init__.py": "",
                    "io.py": "utils/io.py",
                    "config.py": "",
                    "logging.py": ""
                },
                "schemas": {
                    "__init__.py": "",
                    "metadata.json": "schemas/metadata.json"
                }
            }
        },
        "tests": {
            "__init__.py": "",
            "unit": {
                "__init__.py": "",
                "test_orchestrator.py": "",
                "test_agents.py": "",
                "test_api.py": ""
            },
            "integration": {
                "__init__.py": "",
                "test_full_flow.py": ""
            },
            "conftest.py": ""
        },
        "scripts": {
            "start_server.py": "start_api.py",
            "test_setup.py": "test_openai_setup.py",
            "cli.py": "main.py"
        },
        "docs": {
            "README.md": "README.md",
            "API_GUIDE.md": "API_GUIDE.md",
            "DEVELOPMENT.md": "",
            "DEPLOYMENT.md": ""
        },
        "examples": {
            "client_example.py": "api_client_example.py",
            "basic_usage.py": "",
            "advanced_usage.py": ""
        },
        "config": {
            ".env.example": ".env.example",
            "logging.yaml": "",
            "settings.py": ""
        }
    }
    
    return structure

def create_files_and_directories(structure, base_path=".", source_mapping=None):
    """Recursively create directories and files"""
    if source_mapping is None:
        source_mapping = {}
    
    for name, content in structure.items():
        current_path = Path(base_path) / name
        
        if isinstance(content, dict):
            # It's a directory
            current_path.mkdir(exist_ok=True)
            print(f"📁 Created directory: {current_path}")
            create_files_and_directories(content, current_path, source_mapping)
        else:
            # It's a file
            if content and Path(content).exists():
                # Copy existing file
                shutil.copy2(content, current_path)
                print(f"📄 Copied file: {content} -> {current_path}")
            else:
                # Create new file
                current_path.touch()
                print(f"📄 Created file: {current_path}")

def create_init_files():
    """Create proper __init__.py files"""
    
    init_files = {
        "src/report_manager/__init__.py": '''"""
Report Manager Orchestrator

AI-powered orchestrator that processes user text, creates context with LLM,
and coordinates agent calls to fulfill user requests.
"""

__version__ = "1.0.0"
__author__ = "Report Manager Team"

from .core.orchestrator import ReportManagerOrchestrator
from .core.models import UserContext, TaskType, OrchestrationResult
from .core.flow import FlowController

__all__ = [
    "ReportManagerOrchestrator",
    "UserContext", 
    "TaskType",
    "OrchestrationResult",
    "FlowController"
]
''',
        
        "src/report_manager/core/__init__.py": '''"""
Core components of the Report Manager Orchestrator
"""

from .orchestrator import ReportManagerOrchestrator
from .models import UserContext, TaskType, OrchestrationResult, ContextResult, QueryResult
from .flow import FlowController

__all__ = [
    "ReportManagerOrchestrator",
    "UserContext",
    "TaskType", 
    "OrchestrationResult",
    "ContextResult",
    "QueryResult",
    "FlowController"
]
''',
        
        "src/report_manager/agents/__init__.py": '''"""
Agent components for the Report Manager Orchestrator
"""

from .router_agent import RouterAgent
from .query_agent import QueryAgent
from .connectors import connector_manager, MockDataConnector

__all__ = [
    "RouterAgent",
    "QueryAgent", 
    "connector_manager",
    "MockDataConnector"
]
''',
        
        "src/report_manager/api/__init__.py": '''"""
API components for the Report Manager Orchestrator
"""

from .server import app

__all__ = ["app"]
''',
        
        "src/report_manager/utils/__init__.py": '''"""
Utility components for the Report Manager Orchestrator
"""

from .io import IOHandler

__all__ = ["IOHandler"]
'''
    }
    
    for file_path, content in init_files.items():
        Path(file_path).write_text(content.strip())
        print(f"📝 Created __init__.py: {file_path}")

def update_imports_in_files():
    """Update import statements in moved files"""
    
    # Update orchestrator.py imports
    orchestrator_path = Path("src/report_manager/core/orchestrator.py")
    if orchestrator_path.exists():
        content = orchestrator_path.read_text()
        content = content.replace(
            "from models import TaskType, UserContext, ContextResult, OrchestrationResult",
            "from .models import TaskType, UserContext, ContextResult, OrchestrationResult"
        )
        content = content.replace(
            "from utils.io import IOHandler",
            "from ..utils.io import IOHandler"
        )
        orchestrator_path.write_text(content)
        print("✅ Updated orchestrator.py imports")
    
    # Update other files similarly...
    # (This would be expanded for all files)

def create_setup_py():
    """Create setup.py for the package"""
    
    setup_content = '''"""
Setup configuration for Report Manager Orchestrator
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README
this_directory = Path(__file__).parent
long_description = (this_directory / "docs" / "README.md").read_text()

# Read requirements
requirements = (this_directory / "requirements.txt").read_text().splitlines()

setup(
    name="report-manager-orchestrator",
    version="1.0.0",
    author="Report Manager Team",
    author_email="<EMAIL>",
    description="AI-powered orchestrator for report management",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/report-manager-orchestrator",
    package_dir={"": "src"},
    packages=find_packages(where="src"),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
    ],
    python_requires=">=3.9",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.0",
            "flake8>=6.0",
            "mypy>=1.0",
        ],
        "docs": [
            "sphinx>=5.0",
            "sphinx-rtd-theme>=1.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "report-manager=report_manager.cli:main",
            "report-manager-server=report_manager.api.server:main",
        ],
    },
    include_package_data=True,
    package_data={
        "report_manager": [
            "schemas/*.json",
            "config/*.yaml",
        ],
    },
)
'''
    
    Path("setup.py").write_text(setup_content.strip())
    print("📦 Created setup.py")

def create_additional_files():
    """Create additional configuration files"""
    
    # pyproject.toml
    pyproject_content = '''[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "report-manager-orchestrator"
version = "1.0.0"
description = "AI-powered orchestrator for report management"
readme = "docs/README.md"
requires-python = ">=3.9"
license = {text = "MIT"}
authors = [
    {name = "Report Manager Team", email = "<EMAIL>"},
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
]

[project.scripts]
report-manager = "report_manager.cli:main"
report-manager-server = "report_manager.api.server:main"

[tool.black]
line-length = 88
target-version = ['py39']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
'''
    
    Path("pyproject.toml").write_text(pyproject_content.strip())
    print("📋 Created pyproject.toml")
    
    # .gitignore
    gitignore_content = '''# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# Environment variables
.env
.env.local

# Logs
*.log
logs/

# OS
.DS_Store
Thumbs.db

# Testing
.pytest_cache/
.coverage
htmlcov/

# Documentation
docs/_build/
'''
    
    Path(".gitignore").write_text(gitignore_content.strip())
    print("🚫 Created .gitignore")

def main():
    """Main restructuring function"""
    print("🏗️  Restructuring Report Manager Orchestrator Project")
    print("=" * 60)
    
    # Create the new structure
    structure = create_directory_structure()
    create_files_and_directories(structure)
    
    # Create __init__.py files
    create_init_files()
    
    # Create setup files
    create_setup_py()
    create_additional_files()
    
    print("\n" + "=" * 60)
    print("✅ Project restructuring completed!")
    print("\nNew structure created:")
    print("📁 src/report_manager/ - Main package")
    print("📁 tests/ - Test files")
    print("📁 scripts/ - Utility scripts")
    print("📁 docs/ - Documentation")
    print("📁 examples/ - Usage examples")
    print("📁 config/ - Configuration files")
    
    print("\nNext steps:")
    print("1. Review the new structure")
    print("2. Update import statements in moved files")
    print("3. Install in development mode: pip install -e .")
    print("4. Run tests: pytest")

if __name__ == "__main__":
    main()
