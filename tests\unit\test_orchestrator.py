"""
Unit tests for the orchestrator module
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock

from report_manager.core.orchestrator import ReportManagerOrchestrator
from report_manager.core.models import TaskType


@pytest.mark.asyncio
async def test_orchestrator_initialization():
    """Test orchestrator initialization"""
    orchestrator = ReportManagerOrchestrator(
        openai_api_key="test_key",
        model_name="gpt-4",
        temperature=0.1
    )

    assert orchestrator is not None
    assert orchestrator.router_agent is not None
    assert orchestrator.query_agent is not None


@pytest.mark.asyncio
async def test_create_context(mock_orchestrator):
    """Test context creation from user input"""
    user_text = "Show me all reports"

    result = await mock_orchestrator.create_context(user_text)

    assert result is not None
    assert result.context.original_text == user_text
    assert result.context.task_type == TaskType.QUERY
    assert "reports" in result.context.entities


@pytest.mark.asyncio
async def test_process_user_input(mock_orchestrator):
    """Test full user input processing"""
    user_text = "Show me all completed reports"

    result = await mock_orchestrator.process_user_input(user_text)

    assert result is not None
    assert result.success is True
    assert result.context.original_text == user_text
    assert len(result.agents_used) > 0


@pytest.mark.asyncio
async def test_route_to_agents(mock_orchestrator, sample_user_context):
    """Test agent routing"""
    context_result = Mock()
    context_result.context = sample_user_context
    context_result.suggested_agents = ["query_agent"]

    agents = await mock_orchestrator.route_to_agents(context_result)

    assert isinstance(agents, list)
    assert len(agents) > 0
    assert "query_agent" in agents