"""
Query Agent for Report Manager Orchestrator

This agent handles query processing, data retrieval, and basic analysis tasks.
"""

import asyncio
from typing import Dict, List, Any, Optional
from loguru import logger

from models import UserContext, TaskType, QueryResult


class QueryAgent:
    """
    Agent responsible for processing queries, retrieving data,
    and performing basic analysis operations.
    """

    def __init__(self):
        """Initialize the query agent"""
        self.supported_query_types = {
            "data_retrieval": self.handle_data_retrieval,
            "filtering": self.handle_filtering,
            "aggregation": self.handle_aggregation,
            "search": self.handle_search,
            "analysis": self.handle_basic_analysis
        }

        # Mock data sources for demonstration
        self.data_sources = {
            "reports": [
                {"id": 1, "title": "Q1 Sales Report", "type": "sales", "date": "2024-01-15", "status": "completed"},
                {"id": 2, "title": "Marketing Analysis", "type": "marketing", "date": "2024-02-01", "status": "draft"},
                {"id": 3, "title": "Financial Summary", "type": "financial", "date": "2024-02-15", "status": "completed"},
                {"id": 4, "title": "Customer Insights", "type": "customer", "date": "2024-03-01", "status": "in_progress"}
            ],
            "metrics": [
                {"name": "revenue", "value": 150000, "period": "Q1", "trend": "up"},
                {"name": "customers", "value": 1250, "period": "Q1", "trend": "up"},
                {"name": "conversion_rate", "value": 0.045, "period": "Q1", "trend": "stable"},
                {"name": "churn_rate", "value": 0.02, "period": "Q1", "trend": "down"}
            ]
        }

        logger.info("Query agent initialized")

    async def process(self, context: UserContext) -> QueryResult:
        """
        Process a query based on user context

        Args:
            context: User context from orchestrator

        Returns:
            QueryResult with processed data
        """
        logger.info(f"Processing query: {context.intent}")

        try:
            # Determine query type from context
            query_type = await self.determine_query_type(context)

            # Execute the appropriate handler
            if query_type in self.supported_query_types:
                handler = self.supported_query_types[query_type]
                result = await handler(context)
            else:
                result = await self.handle_generic_query(context)

            logger.info(f"Query processed successfully: {query_type}")
            return result

        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            return QueryResult(
                success=False,
                data=None,
                query_type="error",
                metadata={"error_details": str(e)},
                error=str(e)
            )

    async def determine_query_type(self, context: UserContext) -> str:
        """
        Determine the type of query based on context

        Args:
            context: User context

        Returns:
            Query type string
        """
        intent_lower = context.intent.lower()

        # Check for specific keywords to determine query type
        if any(word in intent_lower for word in ["get", "retrieve", "fetch", "show", "list"]):
            return "data_retrieval"
        elif any(word in intent_lower for word in ["filter", "where", "condition"]):
            return "filtering"
        elif any(word in intent_lower for word in ["sum", "count", "average", "total", "aggregate"]):
            return "aggregation"
        elif any(word in intent_lower for word in ["search", "find", "lookup"]):
            return "search"
        elif any(word in intent_lower for word in ["analyze", "analysis", "trend", "insight"]):
            return "analysis"
        else:
            return "data_retrieval"  # Default

    async def handle_data_retrieval(self, context: UserContext) -> QueryResult:
        """Handle data retrieval queries"""
        logger.info("Handling data retrieval query")

        # Extract what data to retrieve from entities and parameters
        entities = [entity.lower() for entity in context.entities]

        retrieved_data = {}

        # Check if user wants reports
        if any(word in entities for word in ["report", "reports"]):
            retrieved_data["reports"] = self.data_sources["reports"]

        # Check if user wants metrics
        if any(word in entities for word in ["metric", "metrics", "kpi", "performance"]):
            retrieved_data["metrics"] = self.data_sources["metrics"]

        # If no specific entities, return all available data
        if not retrieved_data:
            retrieved_data = self.data_sources

        return QueryResult(
            success=True,
            data=retrieved_data,
            query_type="data_retrieval",
            metadata={
                "entities_found": entities,
                "data_sources_accessed": list(retrieved_data.keys())
            }
        )

    async def handle_filtering(self, context: UserContext) -> QueryResult:
        """Handle filtering queries"""
        logger.info("Handling filtering query")

        # Extract filter criteria from parameters
        filters = context.parameters
        entities = [entity.lower() for entity in context.entities]

        filtered_data = {}

        # Filter reports
        if "reports" in self.data_sources:
            reports = self.data_sources["reports"]
            filtered_reports = reports

            # Apply filters
            if "status" in filters:
                filtered_reports = [r for r in filtered_reports if r["status"] == filters["status"]]
            if "type" in filters:
                filtered_reports = [r for r in filtered_reports if r["type"] == filters["type"]]

            filtered_data["reports"] = filtered_reports

        return QueryResult(
            success=True,
            data=filtered_data,
            query_type="filtering",
            metadata={
                "filters_applied": filters,
                "entities_processed": entities
            }
        )

    async def handle_aggregation(self, context: UserContext) -> QueryResult:
        """Handle aggregation queries"""
        logger.info("Handling aggregation query")

        aggregated_data = {}

        # Aggregate reports by status
        if "reports" in self.data_sources:
            reports = self.data_sources["reports"]
            status_counts = {}
            for report in reports:
                status = report["status"]
                status_counts[status] = status_counts.get(status, 0) + 1

            aggregated_data["report_status_counts"] = status_counts

        # Aggregate metrics
        if "metrics" in self.data_sources:
            metrics = self.data_sources["metrics"]
            metric_summary = {
                "total_metrics": len(metrics),
                "trending_up": len([m for m in metrics if m["trend"] == "up"]),
                "trending_down": len([m for m in metrics if m["trend"] == "down"]),
                "stable": len([m for m in metrics if m["trend"] == "stable"])
            }

            aggregated_data["metric_summary"] = metric_summary

        return QueryResult(
            success=True,
            data=aggregated_data,
            query_type="aggregation",
            metadata={
                "aggregation_types": list(aggregated_data.keys())
            }
        )

    async def handle_search(self, context: UserContext) -> QueryResult:
        """Handle search queries"""
        logger.info("Handling search query")

        search_terms = context.entities + list(context.parameters.values())
        search_results = {}

        # Search in reports
        if "reports" in self.data_sources:
            matching_reports = []
            for report in self.data_sources["reports"]:
                for term in search_terms:
                    if (str(term).lower() in report["title"].lower() or
                        str(term).lower() in report["type"].lower()):
                        matching_reports.append(report)
                        break

            search_results["matching_reports"] = matching_reports

        # Search in metrics
        if "metrics" in self.data_sources:
            matching_metrics = []
            for metric in self.data_sources["metrics"]:
                for term in search_terms:
                    if str(term).lower() in metric["name"].lower():
                        matching_metrics.append(metric)
                        break

            search_results["matching_metrics"] = matching_metrics

        return QueryResult(
            success=True,
            data=search_results,
            query_type="search",
            metadata={
                "search_terms": search_terms,
                "results_found": sum(len(v) for v in search_results.values())
            }
        )

    async def handle_basic_analysis(self, context: UserContext) -> QueryResult:
        """Handle basic analysis queries"""
        logger.info("Handling basic analysis query")

        analysis_results = {}

        # Analyze report trends
        if "reports" in self.data_sources:
            reports = self.data_sources["reports"]

            # Status distribution
            status_dist = {}
            for report in reports:
                status = report["status"]
                status_dist[status] = status_dist.get(status, 0) + 1

            # Type distribution
            type_dist = {}
            for report in reports:
                report_type = report["type"]
                type_dist[report_type] = type_dist.get(report_type, 0) + 1

            analysis_results["report_analysis"] = {
                "total_reports": len(reports),
                "status_distribution": status_dist,
                "type_distribution": type_dist,
                "completion_rate": status_dist.get("completed", 0) / len(reports) if reports else 0
            }

        # Analyze metrics
        if "metrics" in self.data_sources:
            metrics = self.data_sources["metrics"]

            trend_analysis = {
                "positive_trends": len([m for m in metrics if m["trend"] == "up"]),
                "negative_trends": len([m for m in metrics if m["trend"] == "down"]),
                "stable_metrics": len([m for m in metrics if m["trend"] == "stable"])
            }

            analysis_results["metric_analysis"] = trend_analysis

        return QueryResult(
            success=True,
            data=analysis_results,
            query_type="analysis",
            metadata={
                "analysis_types": list(analysis_results.keys())
            }
        )

    async def handle_generic_query(self, context: UserContext) -> QueryResult:
        """Handle generic queries that don't fit specific types"""
        logger.info("Handling generic query")

        # Return a summary of available data
        summary = {
            "available_data": {
                "reports": len(self.data_sources.get("reports", [])),
                "metrics": len(self.data_sources.get("metrics", []))
            },
            "context_info": {
                "intent": context.intent,
                "entities": context.entities,
                "task_type": context.task_type.value,
                "confidence": context.confidence
            }
        }

        return QueryResult(
            success=True,
            data=summary,
            query_type="generic",
            metadata={
                "fallback_reason": "No specific query type matched"
            }
        )