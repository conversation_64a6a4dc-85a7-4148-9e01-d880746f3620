"""
Flow Controller for Report Manager Orchestrator

This module manages the execution flow and coordination between
the orchestrator and various agents.
"""

import asyncio
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from loguru import logger

from .models import UserContext, OrchestrationResult


@dataclass
class FlowStep:
    """Represents a single step in the execution flow"""
    name: str
    agent: str
    dependencies: List[str]
    parameters: Dict[str, Any]
    completed: bool = False
    result: Any = None
    error: Optional[str] = None


class FlowController:
    """
    Controls the execution flow of the orchestrator and manages
    complex multi-step workflows.
    """

    def __init__(self, orchestrator):
        """
        Initialize the flow controller

        Args:
            orchestrator: The main orchestrator instance
        """
        self.orchestrator = orchestrator
        self.active_flows: Dict[str, List[FlowStep]] = {}

    async def execute_simple_flow(self, user_text: str) -> OrchestrationResult:
        """
        Execute a simple single-step flow

        Args:
            user_text: User input text

        Returns:
            OrchestrationResult from orchestrator
        """
        logger.info("Executing simple flow")
        return await self.orchestrator.process_user_input(user_text)

    async def execute_complex_flow(self,
                                 user_text: str,
                                 flow_id: str) -> OrchestrationResult:
        """
        Execute a complex multi-step flow

        Args:
            user_text: User input text
            flow_id: Unique identifier for this flow

        Returns:
            OrchestrationResult with combined results
        """
        logger.info(f"Executing complex flow: {flow_id}")

        try:
            # First, get initial context
            context_result = await self.orchestrator.create_context(user_text)
            context = context_result.context

            # Create flow steps based on context
            flow_steps = await self.create_flow_steps(context)
            self.active_flows[flow_id] = flow_steps

            # Execute steps in dependency order
            results = await self.execute_flow_steps(flow_id, context)

            # Clean up
            del self.active_flows[flow_id]

            return OrchestrationResult(
                success=True,
                result=results,
                context=context,
                agents_used=[step.agent for step in flow_steps],
                execution_time=0.0  # TODO: Track actual time
            )

        except Exception as e:
            logger.error(f"Error in complex flow {flow_id}: {str(e)}")
            if flow_id in self.active_flows:
                del self.active_flows[flow_id]

            return OrchestrationResult(
                success=False,
                result=None,
                context=context_result.context if 'context_result' in locals() else None,
                agents_used=[],
                execution_time=0.0,
                error=str(e)
            )

    async def create_flow_steps(self, context: UserContext) -> List[FlowStep]:
        """
        Create flow steps based on user context

        Args:
            context: User context from orchestrator

        Returns:
            List of flow steps to execute
        """
        steps = []

        # Basic flow: always start with routing
        steps.append(FlowStep(
            name="route_request",
            agent="router_agent",
            dependencies=[],
            parameters={"context": context}
        ))

        # Add query step if needed
        if context.task_type.value in ["query", "data_analysis"]:
            steps.append(FlowStep(
                name="process_query",
                agent="query_agent",
                dependencies=["route_request"],
                parameters={"context": context}
            ))

        # Add report generation step if needed
        if context.task_type.value == "report_generation":
            steps.append(FlowStep(
                name="generate_report",
                agent="report_agent",
                dependencies=["route_request"],
                parameters={"context": context}
            ))

        return steps

    async def execute_flow_steps(self,
                               flow_id: str,
                               context: UserContext) -> Dict[str, Any]:
        """
        Execute flow steps in dependency order

        Args:
            flow_id: Flow identifier
            context: User context

        Returns:
            Dictionary of results from each step
        """
        steps = self.active_flows[flow_id]
        results = {}

        # Keep executing until all steps are complete
        while not all(step.completed for step in steps):
            for step in steps:
                if step.completed:
                    continue

                # Check if dependencies are met
                deps_met = all(
                    any(s.name == dep and s.completed for s in steps)
                    for dep in step.dependencies
                ) if step.dependencies else True

                if deps_met:
                    try:
                        logger.info(f"Executing step: {step.name}")

                        # Execute the step
                        if step.agent == "router_agent":
                            result = await self.orchestrator.router_agent.route(
                                context, []
                            )
                        elif step.agent == "query_agent":
                            result = await self.orchestrator.query_agent.process(
                                context
                            )
                        else:
                            logger.warning(f"Unknown agent in step: {step.agent}")
                            result = f"Unknown agent: {step.agent}"

                        step.result = result
                        step.completed = True
                        results[step.name] = result

                        logger.info(f"Step {step.name} completed successfully")

                    except Exception as e:
                        step.error = str(e)
                        step.completed = True
                        results[step.name] = {"error": str(e)}
                        logger.error(f"Step {step.name} failed: {str(e)}")

            # Prevent infinite loop
            await asyncio.sleep(0.1)

        return results

    def get_flow_status(self, flow_id: str) -> Optional[Dict[str, Any]]:
        """
        Get the current status of a flow

        Args:
            flow_id: Flow identifier

        Returns:
            Dictionary with flow status or None if flow not found
        """
        if flow_id not in self.active_flows:
            return None

        steps = self.active_flows[flow_id]

        return {
            "flow_id": flow_id,
            "total_steps": len(steps),
            "completed_steps": sum(1 for step in steps if step.completed),
            "current_step": next(
                (step.name for step in steps if not step.completed),
                "completed"
            ),
            "steps": [
                {
                    "name": step.name,
                    "agent": step.agent,
                    "completed": step.completed,
                    "error": step.error
                }
                for step in steps
            ]
        }