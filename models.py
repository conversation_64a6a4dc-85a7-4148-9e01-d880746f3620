"""
Shared Models for Report Manager Orchestrator

This module contains shared data models and enums used across the system.
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
from pydantic import BaseModel


class TaskType(Enum):
    """Types of tasks the orchestrator can handle"""
    QUERY = "query"
    REPORT_GENERATION = "report_generation"
    DATA_ANALYSIS = "data_analysis"
    WORKFLOW = "workflow"
    UNKNOWN = "unknown"


@dataclass
class UserContext:
    """Context created from user input"""
    original_text: str
    intent: str
    task_type: TaskType
    entities: List[str]
    parameters: Dict[str, Any]
    confidence: float
    metadata: Dict[str, Any]


class ContextResult(BaseModel):
    """Result from context creation"""
    context: UserContext
    reasoning: str
    suggested_agents: List[str]
    
    class Config:
        arbitrary_types_allowed = True


class OrchestrationResult(BaseModel):
    """Final result from orchestration"""
    success: bool
    result: Any
    context: UserContext
    agents_used: List[str]
    execution_time: float
    error: Optional[str] = None
    
    class Config:
        arbitrary_types_allowed = True


@dataclass
class QueryResult:
    """Result from query processing"""
    success: bool
    data: Any
    query_type: str
    metadata: Dict[str, Any]
    error: Optional[str] = None
