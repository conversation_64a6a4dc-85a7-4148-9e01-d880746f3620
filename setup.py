"""
Setup configuration for Report Manager Orchestrator
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README
this_directory = Path(__file__).parent
long_description = (this_directory / "docs" / "README.md").read_text()

# Read requirements
requirements = (this_directory / "requirements.txt").read_text().splitlines()

setup(
    name="report-manager-orchestrator",
    version="1.0.0",
    author="Report Manager Team",
    author_email="<EMAIL>",
    description="AI-powered orchestrator for report management",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/report-manager-orchestrator",
    package_dir={"": "src"},
    packages=find_packages(where="src"),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
    ],
    python_requires=">=3.9",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.0",
            "flake8>=6.0",
            "mypy>=1.0",
        ],
        "docs": [
            "sphinx>=5.0",
            "sphinx-rtd-theme>=1.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "report-manager=report_manager.cli:main",
            "report-manager-server=report_manager.api.server:main",
        ],
    },
    include_package_data=True,
    package_data={
        "report_manager": [
            "schemas/*.json",
            "config/*.yaml",
        ],
    },
)