#!/usr/bin/env python3
"""
Simple startup script for the Report Manager Orchestrator API

This script provides an easy way to start the API server with proper error handling.
"""

import os
import sys
from pathlib import Path

def check_environment():
    """Check if the environment is properly set up"""
    print("🔍 Checking environment...")
    
    # Check if .env file exists
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found!")
        print("   Please copy .env.example to .env and add your OpenAI API key")
        print("   cp .env.example .env")
        return False
    
    # Check if OpenAI API key is set
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key or api_key == "your_openai_api_key_here":
        print("❌ OpenAI API key not set!")
        print("   Please edit .env file and set OPENAI_API_KEY=your_actual_key")
        return False
    
    print("✅ Environment check passed")
    return True


def check_dependencies():
    """Check if required dependencies are installed"""
    print("📦 Checking dependencies...")
    
    required_packages = [
        "fastapi", "uvicorn", "openai", "langchain", 
        "langchain-openai", "pydantic", "python-dotenv",
        "loguru", "typer", "rich"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing packages: {', '.join(missing_packages)}")
        print("   Please install dependencies:")
        print("   pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies installed")
    return True


def start_server():
    """Start the API server"""
    print("🚀 Starting Report Manager Orchestrator API...")
    
    try:
        import uvicorn
        from api_server import app
        
        host = os.getenv("API_HOST", "0.0.0.0")
        port = int(os.getenv("API_PORT", "8000"))
        
        print(f"🌐 Server will start on: http://{host}:{port}")
        print(f"📚 API Documentation: http://{host}:{port}/docs")
        print(f"❤️  Health Check: http://{host}:{port}/health")
        print("\n🔥 Press Ctrl+C to stop the server\n")
        
        uvicorn.run(
            app,
            host=host,
            port=port,
            reload=True,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return False
    
    return True


def main():
    """Main function"""
    print("=" * 60)
    print("🎯 Report Manager Orchestrator API Startup")
    print("=" * 60)
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    # Start server
    if not start_server():
        sys.exit(1)


if __name__ == "__main__":
    main()
