"""
Pytest configuration and fixtures for Report Manager Orchestrator tests
"""

import pytest
import sys
from pathlib import Path
from unittest.mock import Mock, AsyncMock

# Add src to Python path
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from report_manager.core.models import UserContext, TaskType


@pytest.fixture
def sample_user_context():
    """Sample user context for testing"""
    return UserContext(
        original_text="Show me all reports",
        intent="retrieve all reports",
        task_type=TaskType.QUERY,
        entities=["reports"],
        parameters={},
        confidence=0.9,
        metadata={}
    )


@pytest.fixture
def mock_llm():
    """Mock LLM for testing"""
    mock = AsyncMock()
    mock_response = Mock()
    mock_response.content = '''
    {
        "intent": "retrieve reports",
        "task_type": "query",
        "entities": ["reports"],
        "parameters": {},
        "confidence": 0.9,
        "reasoning": "User wants to see reports",
        "suggested_agents": ["query_agent"]
    }
    '''
    mock.ainvoke.return_value = mock_response
    return mock


@pytest.fixture
def mock_orchestrator(mock_llm):
    """Mock orchestrator for testing"""
    from report_manager.core.orchestrator import ReportManagerOrchestrator

    orchestrator = ReportManagerOrchestrator(
        openai_api_key="test_key",
        model_name="gpt-4",
        temperature=0.1
    )
    orchestrator.llm = mock_llm
    return orchestrator