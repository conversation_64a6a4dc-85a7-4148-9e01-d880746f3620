"""
Configuration Management for Report Manager Orchestrator

This module handles configuration loading and validation.
"""

import os
from typing import Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv


class Config:
    """Configuration manager for the application"""

    def __init__(self, env_file: Optional[str] = None):
        """
        Initialize configuration

        Args:
            env_file: Path to environment file (optional)
        """
        # Load environment variables
        if env_file:
            load_dotenv(env_file)
        else:
            load_dotenv()

        self._config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from environment variables"""
        return {
            # OpenAI Configuration
            "openai": {
                "api_key": os.getenv("OPENAI_API_KEY"),
                "model": os.getenv("OPENAI_MODEL", "gpt-4"),
                "temperature": float(os.getenv("OPENAI_TEMPERATURE", "0.1")),
            },

            # Azure OpenAI Configuration
            "azure_openai": {
                "api_key": os.getenv("AZURE_OPENAI_API_KEY"),
                "endpoint": os.getenv("AZURE_OPENAI_ENDPOINT"),
                "deployment": os.getenv("AZURE_OPENAI_DEPLOYMENT"),
                "api_version": os.getenv("AZURE_OPENAI_API_VERSION", "2024-11-01-preview"),
            },

            # API Server Configuration
            "api": {
                "host": os.getenv("API_HOST", "0.0.0.0"),
                "port": int(os.getenv("API_PORT", "8000")),
                "reload": os.getenv("API_RELOAD", "true").lower() == "true",
            },

            # Logging Configuration
            "logging": {
                "level": os.getenv("LOG_LEVEL", "INFO"),
                "file": os.getenv("LOG_FILE", "orchestrator.log"),
            },

            # System Configuration
            "system": {
                "max_agents_per_flow": int(os.getenv("MAX_AGENTS_PER_FLOW", "3")),
                "default_timeout": int(os.getenv("DEFAULT_TIMEOUT", "30")),
                "enable_caching": os.getenv("ENABLE_CACHING", "true").lower() == "true",
            }
        }

    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value by key

        Args:
            key: Configuration key (supports dot notation, e.g., 'openai.api_key')
            default: Default value if key not found

        Returns:
            Configuration value
        """
        keys = key.split('.')
        value = self._config

        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default

        return value

    def get_openai_config(self) -> Dict[str, Any]:
        """Get OpenAI configuration"""
        return self._config["openai"]

    def get_azure_openai_config(self) -> Dict[str, Any]:
        """Get Azure OpenAI configuration"""
        return self._config["azure_openai"]

    def get_api_config(self) -> Dict[str, Any]:
        """Get API server configuration"""
        return self._config["api"]

    def should_use_azure_openai(self) -> bool:
        """Check if Azure OpenAI should be used"""
        azure_config = self.get_azure_openai_config()
        return all([
            azure_config.get("api_key"),
            azure_config.get("endpoint"),
            azure_config.get("deployment")
        ])

    def validate(self) -> bool:
        """
        Validate configuration

        Returns:
            True if configuration is valid
        """
        # Check if at least one AI service is configured
        has_openai = bool(self.get("openai.api_key"))
        has_azure = self.should_use_azure_openai()

        if not (has_openai or has_azure):
            return False

        return True

    def get_validation_errors(self) -> list:
        """
        Get configuration validation errors

        Returns:
            List of validation error messages
        """
        errors = []

        # Check AI service configuration
        has_openai = bool(self.get("openai.api_key"))
        has_azure = self.should_use_azure_openai()

        if not (has_openai or has_azure):
            errors.append("No AI service configured. Set either OPENAI_API_KEY or Azure OpenAI credentials.")

        # Check API configuration
        try:
            port = self.get("api.port")
            if not isinstance(port, int) or port < 1 or port > 65535:
                errors.append("Invalid API port. Must be between 1 and 65535.")
        except (ValueError, TypeError):
            errors.append("Invalid API port configuration.")

        return errors


# Global configuration instance
config = Config()