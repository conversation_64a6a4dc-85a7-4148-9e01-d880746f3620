#!/usr/bin/env python3
"""
Main entry point for Report Manager Orchestrator

This script provides easy access to all functionality.
"""

import sys
import os
from pathlib import Path

# Add src to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def main():
    """Main entry point"""
    if len(sys.argv) < 2:
        print_help()
        return

    command = sys.argv[1]

    if command == "serve" or command == "server":
        start_server()
    elif command == "cli":
        run_cli()
    elif command == "test":
        run_tests()
    elif command == "setup":
        test_setup()
    elif command == "install":
        install_package()
    else:
        print_help()

def print_help():
    """Print help information"""
    print("""
🎯 Report Manager Orchestrator

Usage: python main.py <command>

Commands:
  serve     Start the API server
  cli       Run the CLI interface
  test      Run tests
  setup     Test OpenAI setup
  install   Install package in development mode

Examples:
  python main.py serve          # Start API server
  python main.py cli query "Show me reports"
  python main.py test
  python main.py setup
  python main.py install

Alternative ways to run:
  python scripts/cli.py --help
  python scripts/start_server.py
""")

def start_server():
    """Start the API server"""
    try:
        from dotenv import load_dotenv
        load_dotenv()

        from report_manager.api.server import app
        import uvicorn

        host = os.getenv("API_HOST", "0.0.0.0")
        port = int(os.getenv("API_PORT", "8000"))

        print(f"🚀 Starting Report Manager Orchestrator API on {host}:{port}")
        print(f"📚 API Documentation: http://{host}:{port}/docs")
        print(f"❤️  Health Check: http://{host}:{port}/health")

        uvicorn.run(
            "report_manager.api.server:app",
            host=host,
            port=port,
            reload=True
        )
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        print("Make sure dependencies are installed: pip install -r requirements.txt")

def run_cli():
    """Run the CLI interface"""
    try:
        # Pass remaining arguments to CLI
        import subprocess
        args = ["python", "scripts/cli.py"] + sys.argv[2:]
        subprocess.run(args)
    except Exception as e:
        print(f"❌ Error running CLI: {e}")

def run_tests():
    """Run tests"""
    try:
        import subprocess
        result = subprocess.run(["python", "-m", "pytest", "tests/", "-v"])
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        print("Install pytest: pip install pytest pytest-asyncio")

def test_setup():
    """Test OpenAI setup"""
    try:
        import subprocess
        subprocess.run(["python", "scripts/test_setup.py"])
    except Exception as e:
        print(f"❌ Error testing setup: {e}")

def install_package():
    """Install package in development mode"""
    try:
        import subprocess
        print("📦 Installing Report Manager Orchestrator in development mode...")
        result = subprocess.run(["pip", "install", "-e", "."], check=True)
        print("✅ Package installed successfully!")
        print("You can now use: report-manager serve")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing package: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()