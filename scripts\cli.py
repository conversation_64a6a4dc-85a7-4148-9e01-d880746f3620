"""
Main Application Entry Point for Report Manager Orchestrator

This module provides CLI and API interfaces for user interaction with the orchestrator.
"""

import asyncio
import os
import uuid
from typing import Optional
from dotenv import load_dotenv
import typer
from rich.console import Console
from loguru import logger

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from report_manager.core.orchestrator import ReportManagerOrchestrator
from report_manager.core.flow import FlowController
from report_manager.utils.io import IOHandler
from report_manager.core.models import OrchestrationResult

# Load environment variables
load_dotenv()

# Initialize CLI app
app = typer.Typer(help="Report Manager Orchestrator - AI-powered report management system")
console = Console()

# Global instances
orchestrator: Optional[ReportManagerOrchestrator] = None
flow_controller: Optional[FlowController] = None
io_handler = IOHandler()


def initialize_system():
    """Initialize the orchestrator system"""
    global orchestrator, flow_controller

    # Check for Azure OpenAI configuration
    azure_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")
    azure_deployment = os.getenv("AZURE_OPENAI_DEPLOYMENT")
    azure_api_key = os.getenv("AZURE_OPENAI_API_KEY")

    # Get OpenAI API key (fallback)
    openai_api_key = os.getenv("OPENAI_API_KEY")

    # Determine which service to use
    if azure_endpoint and azure_deployment and azure_api_key:
        # Use Azure OpenAI
        console.print("[blue]Using Azure OpenAI[/blue]")
        orchestrator = ReportManagerOrchestrator(
            openai_api_key=azure_api_key,
            use_azure=True,
            azure_endpoint=f"https://{azure_endpoint}.openai.azure.com/",
            azure_deployment=azure_deployment,
            azure_api_version=os.getenv("AZURE_OPENAI_API_VERSION", "2024-11-01-preview"),
            temperature=float(os.getenv("OPENAI_TEMPERATURE", "0.1"))
        )
    elif openai_api_key:
        # Use regular OpenAI
        console.print("[blue]Using OpenAI[/blue]")
        orchestrator = ReportManagerOrchestrator(
            openai_api_key=openai_api_key,
            model_name=os.getenv("OPENAI_MODEL", "gpt-4"),
            temperature=float(os.getenv("OPENAI_TEMPERATURE", "0.1"))
        )
    else:
        console.print("[red]Error: No API key found. Set either OPENAI_API_KEY or AZURE_OPENAI_API_KEY[/red]")
        raise typer.Exit(1)

    # Initialize flow controller
    flow_controller = FlowController(orchestrator)

    logger.info("System initialized successfully")


@app.command()
def query(
    text: str = typer.Argument(..., help="User query text"),
    output_format: str = typer.Option("json", help="Output format: json, table, text"),
    save_to: Optional[str] = typer.Option(None, help="Save result to file"),
    complex_flow: bool = typer.Option(False, help="Use complex multi-step flow")
):
    """
    Process a user query using the orchestrator
    """
    asyncio.run(process_query_async(text, output_format, save_to, complex_flow))


async def process_query_async(text: str, output_format: str, save_to: Optional[str], complex_flow: bool):
    """Async function to process user query"""
    try:
        # Initialize system if not already done
        if orchestrator is None:
            initialize_system()

        io_handler.display_status(f"Processing query: {text[:50]}...", "info")

        # Process the query
        if complex_flow:
            flow_id = str(uuid.uuid4())
            result = await flow_controller.execute_complex_flow(text, flow_id)
        else:
            result = await flow_controller.execute_simple_flow(text)

        # Display result
        if result.success:
            io_handler.display_status("Query processed successfully", "success")

            # Format and display the result
            formatted_result = {
                "success": result.success,
                "result": result.result,
                "context": {
                    "intent": result.context.intent,
                    "task_type": result.context.task_type.value,
                    "entities": result.context.entities,
                    "confidence": result.context.confidence
                },
                "agents_used": result.agents_used,
                "execution_time": result.execution_time
            }

            io_handler.display_result(formatted_result, "Query Result")

            # Save to file if requested
            if save_to:
                await io_handler.save_to_file(formatted_result, save_to, output_format)
                io_handler.display_status(f"Result saved to {save_to}", "success")

        else:
            io_handler.display_error(f"Query failed: {result.error}", "Query Error")

    except Exception as e:
        logger.error(f"Error processing query: {e}")
        io_handler.display_error(str(e), "System Error")


@app.command()
def interactive():
    """
    Start interactive mode for continuous queries
    """
    asyncio.run(interactive_mode())


async def interactive_mode():
    """Interactive mode for continuous user interaction"""
    try:
        # Initialize system
        initialize_system()

        console.print("[green]Welcome to Report Manager Orchestrator![/green]")
        console.print("Type your queries below. Use 'exit' or 'quit' to stop.\n")

        while True:
            try:
                # Get user input
                user_input = typer.prompt("Query")

                # Check for exit commands
                if user_input.lower() in ['exit', 'quit', 'q']:
                    console.print("[yellow]Goodbye![/yellow]")
                    break

                # Process the query
                await process_query_async(user_input, "json", None, False)
                console.print()  # Add spacing

            except KeyboardInterrupt:
                console.print("\n[yellow]Goodbye![/yellow]")
                break
            except Exception as e:
                io_handler.display_error(str(e), "Error")

    except Exception as e:
        logger.error(f"Error in interactive mode: {e}")
        io_handler.display_error(str(e), "System Error")


@app.command()
def status():
    """
    Show system status and available agents
    """
    try:
        # Initialize system if needed
        if orchestrator is None:
            initialize_system()

        # Get system status
        status_info = {
            "system": "Report Manager Orchestrator",
            "status": "Running",
            "available_agents": orchestrator.router_agent.list_available_agents(),
            "agent_capabilities": {
                agent: orchestrator.router_agent.get_agent_info(agent)
                for agent in orchestrator.router_agent.list_available_agents()
            }
        }

        io_handler.display_result(status_info, "System Status")

    except Exception as e:
        logger.error(f"Error getting status: {e}")
        io_handler.display_error(str(e), "Status Error")


@app.command()
def test_connection():
    """
    Test connection to LLM service
    """
    asyncio.run(test_connection_async())


@app.command()
def serve(
    host: str = typer.Option("0.0.0.0", help="Host to bind the server to"),
    port: int = typer.Option(8000, help="Port to bind the server to"),
    reload: bool = typer.Option(True, help="Enable auto-reload for development")
):
    """
    Start the API server
    """
    import uvicorn

    console.print(f"[green]Starting API server on {host}:{port}[/green]")
    console.print(f"[blue]API Documentation: http://{host}:{port}/docs[/blue]")
    console.print(f"[blue]Health Check: http://{host}:{port}/health[/blue]")

    # Set environment variables for the server
    os.environ["API_HOST"] = host
    os.environ["API_PORT"] = str(port)
    os.environ["API_RELOAD"] = str(reload).lower()

    try:
        uvicorn.run(
            "api_server:app",
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
    except KeyboardInterrupt:
        console.print("\n[yellow]Server stopped[/yellow]")
    except Exception as e:
        console.print(f"[red]Error starting server: {e}[/red]")


@app.command()
def api_test():
    """
    Test the API server with example requests
    """
    console.print("[green]Testing API server...[/green]")

    try:
        import subprocess
        import sys

        # Run the API client example
        result = subprocess.run([sys.executable, "api_client_example.py"],
                              capture_output=True, text=True)

        if result.returncode == 0:
            console.print(result.stdout)
        else:
            console.print(f"[red]API test failed:[/red]")
            console.print(result.stderr)

    except Exception as e:
        console.print(f"[red]Error running API test: {e}[/red]")
        console.print("[yellow]Make sure the API server is running:[/yellow]")
        console.print("python main.py serve")


async def test_connection_async():
    """Test LLM connection"""
    try:
        # Initialize system
        initialize_system()

        io_handler.display_status("Testing LLM connection...", "info")

        # Test with a simple query
        test_result = await orchestrator.create_context("Hello, test connection")

        if test_result.context.confidence > 0:
            io_handler.display_status("LLM connection successful", "success")
            io_handler.display_result({
                "test_query": "Hello, test connection",
                "response_received": True,
                "confidence": test_result.context.confidence,
                "intent": test_result.context.intent
            }, "Connection Test Result")
        else:
            io_handler.display_error("LLM connection failed - low confidence response", "Connection Error")

    except Exception as e:
        logger.error(f"Connection test failed: {e}")
        io_handler.display_error(str(e), "Connection Test Error")


if __name__ == "__main__":
    # Configure logging
    logger.add("orchestrator.log", rotation="1 day", retention="7 days")

    # Run the CLI app
    app()