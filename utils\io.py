"""
IO Handler for Report Manager Orchestrator

This module handles input/output operations, formatting, and data serialization.
"""

import json
import asyncio
from typing import Any, Dict, List, Optional
from datetime import datetime
from loguru import logger
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.json import JSON


class IOHandler:
    """
    Handles input/output operations for the orchestrator system
    """

    def __init__(self):
        """Initialize the IO handler"""
        self.console = Console()

    def format_output(self, data: Any, format_type: str = "json") -> str:
        """
        Format data for output

        Args:
            data: Data to format
            format_type: Type of formatting (json, table, text)

        Returns:
            Formatted string
        """
        if format_type == "json":
            return self.format_as_json(data)
        elif format_type == "table":
            return self.format_as_table(data)
        elif format_type == "text":
            return self.format_as_text(data)
        else:
            return str(data)

    def format_as_json(self, data: Any) -> str:
        """Format data as JSON"""
        try:
            return json.dumps(data, indent=2, default=str)
        except Exception as e:
            logger.warning(f"Failed to format as JSON: {e}")
            return str(data)

    def format_as_table(self, data: Any) -> str:
        """Format data as a table (for lists of dictionaries)"""
        if not isinstance(data, list) or not data:
            return self.format_as_json(data)

        if not isinstance(data[0], dict):
            return self.format_as_json(data)

        # Create table
        table = Table()

        # Add columns
        columns = list(data[0].keys())
        for col in columns:
            table.add_column(col.title())

        # Add rows
        for item in data:
            row = [str(item.get(col, "")) for col in columns]
            table.add_row(*row)

        # Capture table output
        with self.console.capture() as capture:
            self.console.print(table)

        return capture.get()

    def format_as_text(self, data: Any) -> str:
        """Format data as readable text"""
        if isinstance(data, dict):
            lines = []
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    lines.append(f"{key.title()}:")
                    lines.append(self.format_as_text(value))
                else:
                    lines.append(f"{key.title()}: {value}")
            return "\n".join(lines)
        elif isinstance(data, list):
            return "\n".join([f"- {self.format_as_text(item)}" for item in data])
        else:
            return str(data)

    def display_result(self, result: Any, title: str = "Result"):
        """
        Display result using rich formatting

        Args:
            result: Result to display
            title: Title for the display
        """
        if isinstance(result, dict):
            json_data = JSON(json.dumps(result, default=str))
            panel = Panel(json_data, title=title, expand=False)
            self.console.print(panel)
        else:
            panel = Panel(str(result), title=title, expand=False)
            self.console.print(panel)

    def display_error(self, error: str, title: str = "Error"):
        """
        Display error message

        Args:
            error: Error message
            title: Title for the error display
        """
        panel = Panel(error, title=title, style="red", expand=False)
        self.console.print(panel)

    def display_status(self, message: str, status: str = "info"):
        """
        Display status message

        Args:
            message: Status message
            status: Status type (info, success, warning, error)
        """
        styles = {
            "info": "blue",
            "success": "green",
            "warning": "yellow",
            "error": "red"
        }

        style = styles.get(status, "blue")
        self.console.print(f"[{style}]{message}[/{style}]")

    async def save_to_file(self, data: Any, filename: str, format_type: str = "json"):
        """
        Save data to file

        Args:
            data: Data to save
            filename: Output filename
            format_type: Format for saving
        """
        try:
            formatted_data = self.format_output(data, format_type)

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(formatted_data)

            logger.info(f"Data saved to {filename}")

        except Exception as e:
            logger.error(f"Failed to save data to {filename}: {e}")
            raise

    async def load_from_file(self, filename: str) -> Any:
        """
        Load data from file

        Args:
            filename: Input filename

        Returns:
            Loaded data
        """
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()

            # Try to parse as JSON first
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                # Return as text if not JSON
                return content

        except Exception as e:
            logger.error(f"Failed to load data from {filename}: {e}")
            raise